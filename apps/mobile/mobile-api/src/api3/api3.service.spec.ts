import { Api3Service } from './api3.service';
import { BadRequestException } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';

import {
  Api3TokenModule,
  CACHED_API3_TOKEN,
  NON_EXPIRING_TTL,
} from '@experience/mobile/nest/api3-token';
import { AuthModule } from '@experience/mobile/nest/auth-service';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import { InfoApi as FirmwareInfoApi } from '@experience/shared/axios/firmware-upgrade-client';
import { ITokenAuthUser } from '@experience/mobile/nest/authorisation';
import { LocalesModule } from '../locales/locales.module';
import { LocalesService } from '../locales/locales.service';
import {
  PodUnits,
  TEST_POD_UNIT_ENTITY,
  TEST_USER_ENTITY,
  Users,
} from '@experience/shared/sequelize/podadmin';
import {
  TEST_FIRMWARE_STATUS_TYPES,
  TEST_OUT_OF_DATE_FIRMWARE_STATUS_TYPE,
} from '@experience/shared/axios/firmware-upgrade-client/fixtures';
import { TariffRequest } from './api3.types';
import { Test, TestingModule } from '@nestjs/testing';
import { UserNotFoundException } from '../users/users.exception';
import { jest } from '@jest/globals';
import axios, { AxiosResponse } from 'axios';
import jwt from 'jsonwebtoken';
import users from './__stubs__/users.json';

jest.mock('axios');
const mockAuth = {
  updateUser: jest.fn(),
};

jest.mock('@experience/shared/firebase/admin', () => ({
  getAuth: () => mockAuth,
}));

describe('Api3Service', () => {
  let service: Api3Service;
  let localesService: LocalesService;
  let usersRepository: typeof Users;
  let podUnitsRepository: typeof PodUnits;
  let firmwareInfoApi: FirmwareInfoApi;
  const uid = 'uid';
  const id = '12345';
  let cache: Cache;
  const TOKEN = jwt.sign(
    { aud: process.env.DRIVER_AUTH_AUDIENCE },
    process.env.DRIVER_AUTH_PRIVATE_KEY,
    {
      algorithm: 'RS256',
      subject: id,
    }
  );

  const queryParam = {
    include:
      'account,account.payment,vehicle,vehicle.make,vehicle.socket,group,notifications,unit.pod.unit_connectors,unit.pod.address.tariff.tiers,unit.pod.address.tariff.energy_supplier,unit.pod.model',
  };

  const authUser: ITokenAuthUser = {
    uuid: uid,
    email: TEST_USER_ENTITY.email,
    id,
  };

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [Api3TokenModule, AuthModule, ConfigModule, LocalesModule],
      providers: [
        Api3Service,
        { provide: 'USERS_REPOSITORY', useValue: Users },
        { provide: 'POD_UNITS_REPOSITORY', useValue: PodUnits },
        { provide: FirmwareInfoApi, useValue: new FirmwareInfoApi() },
      ],
    }).compile();

    service = module.get<Api3Service>(Api3Service);
    localesService = module.get<LocalesService>(LocalesService);
    firmwareInfoApi = module.get<FirmwareInfoApi>(FirmwareInfoApi);
    usersRepository = module.get<typeof Users>('USERS_REPOSITORY');
    podUnitsRepository = module.get<typeof PodUnits>('POD_UNITS_REPOSITORY');
    cache = module.get<Cache>(CACHE_MANAGER);
    await cache.set(CACHED_API3_TOKEN, TOKEN, NON_EXPIRING_TTL);
  });

  describe('api3Service getUserInfo', () => {
    it('should be defined', () => {
      expect(service).toBeDefined();
      expect(usersRepository).toBeDefined();
      expect(podUnitsRepository).toBeDefined();
      expect(firmwareInfoApi).toBeDefined();
      expect(localesService).toBeDefined();
    });
    it('should return user info', async () => {
      const mockFindUserRepository = jest
        .spyOn(usersRepository, 'findOne')
        .mockResolvedValueOnce(TEST_USER_ENTITY);
      jest.spyOn(axios, 'get').mockResolvedValueOnce(users);

      await service.getUser(uid, queryParam, id);
      expect(mockFindUserRepository).toHaveBeenCalledWith({
        where: { authId: uid },
      });
      expect(axios.get).toHaveBeenCalledWith(
        `${process.env.API3_BASE_URL}/v5/users/${TEST_USER_ENTITY.id}?include=${queryParam.include}`,
        { headers: { Authorization: 'Bearer ' + TOKEN } }
      );
    });

    it('should throw an exception when no user found', async () => {
      jest.spyOn(usersRepository, 'findOne').mockResolvedValueOnce(null);

      await expect(service.getUser(uid, queryParam)).rejects.toThrow(
        UserNotFoundException
      );
    });

    it('should throw an error if the user is deactivated', async () => {
      jest.spyOn(usersRepository, 'findOne').mockResolvedValueOnce({
        ...TEST_USER_ENTITY,
        deletedAt: new Date(),
      } as Users);

      await expect(service.getUser(uid, queryParam)).rejects.toThrow(
        UserNotFoundException
      );
    });

    it('should throw an exception when user is found but api3 fails', async () => {
      jest
        .spyOn(usersRepository, 'findOne')
        .mockResolvedValueOnce(TEST_USER_ENTITY);
      jest.spyOn(axios, 'get').mockRejectedValue(new BadRequestException());

      await expect(service.getUser(uid, queryParam)).rejects.toThrow(
        new BadRequestException()
      );
    });
  });

  describe('resetPassword', () => {
    it('should throw an exception when reset password request fails', async () => {
      jest.spyOn(axios, 'post').mockRejectedValue(new BadRequestException());

      await expect(
        service.resetPassword({ email: '<EMAIL>' })
      ).rejects.toThrow(new BadRequestException());
    });

    it('should return success when request is successful', async () => {
      jest
        .spyOn(axios, 'post')
        .mockResolvedValue({ data: { message: 'success' } });

      const response = await service.resetPassword({ email: '<EMAIL>' });
      expect(response).toEqual({ message: 'success' });
    });
  });

  describe('reportUserToBeDeleted', () => {
    it('should report a user as to be deleted', async () => {
      const mockFindUserRepository = jest
        .spyOn(usersRepository, 'findOne')
        .mockResolvedValueOnce(TEST_USER_ENTITY);
      jest.spyOn(axios, 'post').mockResolvedValueOnce([]);

      await service.reportUserToBeDeleted(authUser);
      expect(mockFindUserRepository).toHaveBeenCalledWith({
        where: { authId: authUser.uuid },
      });
      expect(axios.post).toHaveBeenCalledWith(
        `${process.env.API3_BASE_URL}/v5/feedback`,
        {
          email: authUser.email,
          description: `Please delete user account for:  ${authUser.email}`,
          issue: 'Delete user',
          user: TEST_USER_ENTITY.id,
        },
        { headers: { Authorization: 'Bearer ' + TOKEN } }
      );
    });

    it('should throw an exception when no user found', async () => {
      jest.spyOn(usersRepository, 'findOne').mockResolvedValueOnce(null);

      await expect(service.reportUserToBeDeleted(authUser)).rejects.toThrow(
        UserNotFoundException
      );
    });

    it('should throw an error if the user is deactivated', async () => {
      jest.spyOn(usersRepository, 'findOne').mockResolvedValueOnce({
        ...TEST_USER_ENTITY,
        deletedAt: new Date(),
      } as Users);

      await expect(service.reportUserToBeDeleted(authUser)).rejects.toThrow(
        UserNotFoundException
      );
    });

    it('should throw an exception when user is found but api3 fails', async () => {
      jest
        .spyOn(usersRepository, 'findOne')
        .mockResolvedValueOnce(TEST_USER_ENTITY);
      jest.spyOn(axios, 'post').mockRejectedValue(new BadRequestException());

      await expect(service.reportUserToBeDeleted(authUser)).rejects.toThrow(
        new BadRequestException()
      );
    });
  });
  describe('getCurrentFirmWare', () => {
    it.each([
      ['with no firmware', [], []],
      ['with firmware', TEST_FIRMWARE_STATUS_TYPES, TEST_FIRMWARE_STATUS_TYPES],
      [
        'with out of date firmware',
        TEST_OUT_OF_DATE_FIRMWARE_STATUS_TYPE,
        TEST_OUT_OF_DATE_FIRMWARE_STATUS_TYPE,
      ],
    ])(
      'should find by unitId %s',
      async (title, firmwareApiResponse, expectedResponse) => {
        const mockFindPodUnitRepository = jest
          .spyOn(podUnitsRepository, 'findOne')
          .mockResolvedValueOnce(TEST_POD_UNIT_ENTITY);
        jest
          .spyOn(firmwareInfoApi, 'getCurrentFirmware')
          .mockResolvedValueOnce({
            data: firmwareApiResponse,
          } as AxiosResponse);
        const response = await service.getCurrentFirmware(
          TEST_POD_UNIT_ENTITY.id
        );
        expect(mockFindPodUnitRepository).toHaveBeenCalledWith({
          where: { id: TEST_POD_UNIT_ENTITY.id },
        });
        expect(response).toEqual(expectedResponse);
      }
    );

    it('should return an empty array when the downstream api throws an error', async () => {
      jest
        .spyOn(podUnitsRepository, 'findOne')
        .mockResolvedValueOnce(TEST_POD_UNIT_ENTITY);
      jest
        .spyOn(firmwareInfoApi, 'getCurrentFirmware')
        .mockRejectedValue(new Error('problem'));
      const response = await service.getCurrentFirmware(12345);
      expect(response).toEqual([]);
    });

    it('should return an empty array when the sequelize throws an error', async () => {
      jest.spyOn(podUnitsRepository, 'findOne').mockResolvedValueOnce(null);
      jest
        .spyOn(firmwareInfoApi, 'getCurrentFirmware')
        .mockRejectedValue(new Error('problem'));
      const respone = await service.getCurrentFirmware(12345);
      expect(respone).toEqual([]);
    });
  });

  describe('storeTariffInfo', () => {
    it('should store tariff info and add energy supplier', async () => {
      const response = { id: '9571' };
      const tariffRequest: TariffRequest = {
        user_id: 1,
        energy_supplier_id: null,
        tiers: [{ rate: 1.19 }],
      };
      jest.spyOn(axios, 'post').mockResolvedValueOnce(response);

      await service.storeTariff(id, tariffRequest);
      expect(axios.post).toHaveBeenCalledWith(
        `${process.env.API3_BASE_URL}/v5/tariffs`,
        tariffRequest,
        { headers: { Authorization: 'Bearer ' + TOKEN } }
      );
      expect(tariffRequest.energy_supplier_id).toEqual(64);
    });

    it('should store tariff info and keep energy supplier', async () => {
      const response = { id: '9571' };
      const tariffRequest: TariffRequest = {
        user_id: 1,
        energy_supplier_id: 10,
        tiers: [{ rate: 1.19 }],
      };
      jest.spyOn(axios, 'post').mockResolvedValueOnce(response);

      await service.storeTariff(id, tariffRequest);
      expect(axios.post).toHaveBeenCalledWith(
        `${process.env.API3_BASE_URL}/v5/tariffs`,
        tariffRequest,
        { headers: { Authorization: 'Bearer ' + TOKEN } }
      );
      expect(tariffRequest.energy_supplier_id).toEqual(10);
    });
  });

  describe('getLocales', () => {
    it('should return locales', async () => {
      const response = await service.getLocales('en-GB');

      expect(response.data).toHaveLength(5);
      expect(response.data[0]).toEqual({ id: 'en', name: 'United Kingdom' });
    });
  });

  describe('claimCharge()', () => {
    it("calls API3's POST /chargers endpoint with a refreshed token", async () => {
      const axiosPostMock = jest.spyOn(axios, 'post').mockResolvedValue({
        data: {},
      });

      const request = {
        user: 1,
        pod: 2,
        door: 3,
        claimed_by: 4,
      };

      await service.claimCharge(authUser, request);

      expect(axiosPostMock).toHaveBeenCalledWith(
        'https://api-staging.pod-point.com/v5/charges',
        request,
        {
          headers: {
            Authorization: `Bearer ${TOKEN}`,
          },
        }
      );
    });
  });

  describe('topUpAccount()', () => {
    it("calls API3's POST /account/topup endpoint with a refreshed token", async () => {
      const axiosPostMock = jest.spyOn(axios, 'post').mockResolvedValue({
        data: {},
      });

      const request = {
        id: 1,
        currency: 'GBP',
        amount: 500,
        card_id: 'abc134',
        token: '123abc',
      };

      await service.topUpAccount(authUser, '1', request);

      expect(axiosPostMock).toHaveBeenCalledWith(
        'https://api-staging.pod-point.com/v5/users/1/account/topup',
        request,
        {
          headers: {
            Authorization: `Bearer ${TOKEN}`,
          },
        }
      );
    });
  });
});
