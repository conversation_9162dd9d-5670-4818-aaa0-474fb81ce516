import {
  <PERSON>,
  Get,
  Param,
  ParseIntPipe,
  Query,
  Res,
  SerializeOptions,
  StreamableFile,
} from '@nestjs/common';
import { FeatureFlag, generateCsvFile } from '@experience/shared/nest/utils';
import { GroupId, GroupService, GroupUid } from '@experience/commercial/site-admin/nest/admin-module';
import {
  Insight,
  defaultInsightsCsvColumns,
  generateInsightsCsvFileName,
} from '@experience/commercial/site-admin/typescript/domain-model';
import { InsightsService } from './insights.service';
import { PodService } from '@experience/commercial/site-admin/nest/pod-module';
import { Response } from 'express';
import { SiteService } from '@experience/commercial/site-admin/nest/site-module';

@Controller('insights')
@SerializeOptions({ exposeUnsetFields: false })
export class InsightsController {
  constructor(
    private insightsService: InsightsService,
    private groupService: GroupService,
    private siteService: SiteService,
    private podService: PodService
  ) {}

  @Get()
  async findByGroupUid(
    @GroupUid() groupUid: string,
    @Query('year') year?: number,
    @FeatureFlag('useChargeProjectionEndpoints')
    useChargeProjectionEndpoints = false
  ): Promise<Insight[]> {
    return await this.insightsService.findByGroupUid(
      groupUid,
      year,
      this.useChargeProjectionEndpoints(useChargeProjectionEndpoints, year)
    );
  }

  @Get('/csv')
  async generateCsvByGroupUid(
    @Res({ passthrough: true }) res: Response,
    @GroupId() groupId: number,
    @GroupUid() groupUid: string,
    @Query('year') year?: number,
    @FeatureFlag('useChargeProjectionEndpoints')
    useChargeProjectionEndpoints = false
  ): Promise<StreamableFile> {
    const results = await this.insightsService.findByGroupUid(
      groupUid,
      year,
      this.useChargeProjectionEndpoints(useChargeProjectionEndpoints, year)
    );
    const group = await this.groupService.findByGroupId(groupId);

    const filename = generateInsightsCsvFileName(
      group.name,
      results[0].intervalStartDate,
      year
    );

    res.set({
      'Content-Type': 'text/csv; charset=UTF-8',
      'Content-Disposition': `attachment; filename=${filename}`,
    });

    return generateCsvFile(defaultInsightsCsvColumns, results, {
      currency: ['revenueGenerated', 'cost'],
      monthAndYear: ['intervalStartDate'],
    });
  }

  @Get('sites/:siteId')
  async findByGroupUidAndSiteId(
    @GroupUid() groupUid: string,
    @Param('siteId', ParseIntPipe) siteId: number,
    @Query('year') year?: number,
    @FeatureFlag('useChargeProjectionEndpoints')
    useChargeProjectionEndpoints = false
  ): Promise<Insight[]> {
    return await this.insightsService.findByGroupUidAndSiteId(
      groupUid,
      siteId,
      year,
      this.useChargeProjectionEndpoints(useChargeProjectionEndpoints, year)
    );
  }

  @Get('sites/:siteId/csv')
  async generateCsvByGroupUidAndSiteId(
    @Res({ passthrough: true }) res: Response,
    @GroupUid() groupUid: string,
    @Param('siteId', ParseIntPipe) siteId: number,
    @Query('year') year?: number,
    @FeatureFlag('useChargeProjectionEndpoints')
    useChargeProjectionEndpoints = false
  ): Promise<StreamableFile> {
    const results = await this.insightsService.findByGroupUidAndSiteId(
      groupUid,
      siteId,
      year,
      this.useChargeProjectionEndpoints(useChargeProjectionEndpoints, year)
    );
    const site = await this.siteService.findByGroupUidAndSiteId({
      groupUid,
      siteId,
    });

    const filename = generateInsightsCsvFileName(
      [site.address.name, site.address.postcode].filter(Boolean).join('-'),
      results[0].intervalStartDate,
      year
    );

    res.set({
      'Content-Type': 'text/csv; charset=UTF-8',
      'Content-Disposition': `attachment; filename=${filename}`,
    });

    return generateCsvFile(defaultInsightsCsvColumns, results, {
      currency: ['revenueGenerated', 'cost'],
      monthAndYear: ['intervalStartDate'],
    });
  }

  @Get('chargers/:chargerId')
  async findByGroupUidAndChargerId(
    @GroupUid() groupUid: string,
    @Param('chargerId') chargerId: number | string,
    @Query('year') year?: number,
    @FeatureFlag('useChargeProjectionEndpoints')
    useChargeProjectionEndpoints = false
  ): Promise<Insight[]> {
    return await this.insightsService.findByGroupUidAndChargerId(
      groupUid,
      this.useChargeProjectionEndpoints(useChargeProjectionEndpoints, year)
        ? chargerId
        : parseInt(chargerId as string),
      year,
      this.useChargeProjectionEndpoints(useChargeProjectionEndpoints, year)
    );
  }

  @Get('chargers/:chargerId/csv')
  async generateCsvByGroupUidAndChargerId(
    @Res({ passthrough: true }) res: Response,
    @GroupId() groupId: number,
    @GroupUid() groupUid: string,
    @Param('chargerId') chargerId: number | string,
    @Query('year') year?: number,
    @FeatureFlag('useChargeProjectionEndpoints')
    useChargeProjectionEndpoints = false
  ): Promise<StreamableFile> {
    const results = await this.insightsService.findByGroupUidAndChargerId(
      groupUid,
      this.useChargeProjectionEndpoints(useChargeProjectionEndpoints)
        ? chargerId
        : parseInt(chargerId as string),
      year,
      this.useChargeProjectionEndpoints(useChargeProjectionEndpoints)
    );
    const charger = await this.podService.findByGroupIdAndPodId(
      groupId,
      chargerId
    );

    const filename = generateInsightsCsvFileName(
      charger.name,
      results[0].intervalStartDate,
      year
    );

    res.set({
      'Content-Type': 'text/csv; charset=UTF-8',
      'Content-Disposition': `attachment; filename=${filename}`,
    });

    return generateCsvFile(defaultInsightsCsvColumns, results, {
      currency: ['revenueGenerated', 'cost'],
      monthAndYear: ['intervalStartDate'],
    });
  }

  private useChargeProjectionEndpoints(
    useChargeProjectionEndpoints: boolean,
    year?: number
  ) {
    if (year) {
      return year >= 2023 && useChargeProjectionEndpoints;
    }
    return useChargeProjectionEndpoints;
  }
}
