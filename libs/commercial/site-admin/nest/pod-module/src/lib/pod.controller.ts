import {
  <PERSON>,
  Get,
  Param,
  ParseIntPipe,
  Query,
  Res,
  SerializeOptions,
  StreamableFile,
  UseInterceptors,
} from '@nestjs/common';
import { FeatureFlag, generateCsvFile } from '@experience/shared/nest/utils';
import {
  Pod,
  defaultChargeCsvColumns,
} from '@experience/commercial/site-admin/typescript/domain-model';
import { GroupId, GroupUid } from '@experience/commercial/site-admin/nest/admin-module';
import { PodInterceptor } from './pod.interceptor';
import { PodService } from './pod.service';
import { Response } from 'express';
import { SecurityEvents } from '@experience/shared/axios/diagnostics-service-client';
import dayjs from 'dayjs';

@Controller('pods')
@UseInterceptors(PodInterceptor)
@SerializeOptions({ exposeUnsetFields: false })
export class PodController {
  constructor(private podService: PodService) {}

  @Get()
  async findByGroupId(@GroupId() groupId: number): Promise<Pod[]> {
    return this.podService.findByGroupId(groupId);
  }

  @Get(':podId')
  async findByGroupIdAndPodId(
    @GroupId() groupId: number,
    @Param('podId') podId: string,
    @FeatureFlag('useChargeProjectionEndpoints')
    useChargeProjectionEndpoints = false
  ): Promise<Pod> {
    return this.podService.findByGroupIdAndPodId(
      groupId,
      podId,
      useChargeProjectionEndpoints
    );
  }

  @Get(':podId/charges')
  async generateCsv(
    @GroupId() groupId: number,
    @GroupUid() groupUid: string,
    @Param('podId', ParseIntPipe) podId: number,
    @Res({ passthrough: true }) res: Response,
    @Query('date') date?: string,
    @FeatureFlag('useChargeProjectionEndpoints')
    useChargeProjectionEndpoints = false
  ): Promise<StreamableFile> {
    const { filename, chargeData } =
      await this.podService.findChargeDataByGroupIdAndPodId(
        groupId,
        groupUid,
        podId,
        date,
        dayjs(date).year() >= 2023 && useChargeProjectionEndpoints
      );

    res.set({
      'Content-Type': 'text/csv; charset=UTF-8',
      'Content-Disposition': `attachment; filename=${filename}`,
    });

    const { podName, ...columns } = defaultChargeCsvColumns;

    return generateCsvFile(columns, chargeData, {
      boolean: ['confirmed'],
      time: ['chargingDuration', 'totalDuration'],
    });
  }

  @Get(':podId/events/security')
  async findSecurityEventsByGroupIdAndPodId(
    @Param('podId') podId: string,
    @GroupId() groupId: number
  ): Promise<SecurityEvents> {
    return this.podService.findSecurityEventsByGroupIdAndPodId(groupId, podId);
  }
}
