import {
  Admin,
  CreateAdminRequest,
} from '@experience/commercial/site-admin/typescript/domain-model';
import { AdminInterceptor } from './admin.interceptor';
import { AdminService } from './admin.service';
import { AdminId, AdminUid, GroupId } from './admin-param-decorators';
import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  Param,
  ParseIntPipe,
  ParseUUIDPipe,
  Post,
  UseInterceptors,
  ValidationPipe,
} from '@nestjs/common';
import { Referer } from '@experience/shared/nest/utils';
import { TransactionInterceptor } from '@experience/shared/sequelize/podadmin';

@Controller('admins')
@UseInterceptors(AdminInterceptor)
export class AdminController {
  constructor(private adminService: AdminService) {}

  @Get()
  async findByGroupId(@GroupId() groupId: number): Promise<Admin[]> {
    return this.adminService.findByGroupId(groupId);
  }

  @Post()
  @UseInterceptors(TransactionInterceptor)
  async createByGroupId(
    @GroupId() groupId: number,
    @AdminUid() currentAdminUid: string,
    @Body(ValidationPipe) request: CreateAdminRequest,
    @Referer() referer: URL
  ): Promise<void> {
    return this.adminService.createByGroupId(
      groupId,
      currentAdminUid,
      request,
      referer
    );
  }

  @Post(':id/status')
  @HttpCode(200)
  async reactivateByGroupIdAndAdminId(
    @Param('id', ParseIntPipe) adminId: number,
    @GroupId() groupId: number,
    @AdminId() currentAdminId: number
  ): Promise<void> {
    return this.adminService.reactivateByGroupIdAndAdminId(
      groupId,
      adminId,
      currentAdminId
    );
  }

  @Delete(':id')
  @HttpCode(204)
  async deleteByGroupId(
    @Param('id', ParseIntPipe) adminId: number,
    @GroupId() groupId: number,
    @AdminId() currentAdminId: number
  ): Promise<void> {
    return this.adminService.deleteByGroupIdAndAdminId(
      groupId,
      adminId,
      currentAdminId
    );
  }

  @Post(':id/invitation')
  @HttpCode(204)
  async inviteByGroupIdAndDriverId(
    @Param('id', ParseIntPipe) adminId: number,
    @GroupId() groupId: number,
    @AdminUid() currentAdminUid: string,
    @Referer() referer: URL
  ): Promise<void> {
    await this.adminService.inviteByGroupIdAndAdminId(
      groupId,
      adminId,
      currentAdminUid,
      referer
    );
  }
}
