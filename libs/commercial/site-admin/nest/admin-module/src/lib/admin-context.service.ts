import { Injectable, Scope } from '@nestjs/common';

export interface AdminContext {
  adminId: number;
  adminUid: string;
  groupId: number;
  groupUid: string;
}

@Injectable({ scope: Scope.REQUEST })
export class AdminContextService {
  private context?: AdminContext;

  setContext(context: AdminContext): void {
    this.context = context;
  }

  getContext(): AdminContext | undefined {
    return this.context;
  }

  getAdminId(): number {
    if (!this.context) {
      throw new Error('Admin context not set');
    }
    return this.context.adminId;
  }

  getAdminUid(): string {
    if (!this.context) {
      throw new Error('Admin context not set');
    }
    return this.context.adminUid;
  }

  getGroupId(): number {
    if (!this.context) {
      throw new Error('Admin context not set');
    }
    return this.context.groupId;
  }

  getGroupUid(): string {
    if (!this.context) {
      throw new Error('Admin context not set');
    }
    return this.context.groupUid;
  }
}
