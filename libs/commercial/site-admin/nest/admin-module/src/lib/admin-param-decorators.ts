import { ExecutionContext, createParamDecorator } from '@nestjs/common';
import { AdminContextService } from './admin-context.service';

export const AdminId = createParamDecorator(
  (_data: unknown, ctx: ExecutionContext): number => {
    const adminContextService = ctx.switchToHttp().getRequest().adminContextService as AdminContextService;
    return adminContextService.getAdminId();
  }
);

export const AdminUid = createParamDecorator(
  (_data: unknown, ctx: ExecutionContext): string => {
    const adminContextService = ctx.switchToHttp().getRequest().adminContextService as AdminContextService;
    return adminContextService.getAdminUid();
  }
);

export const GroupId = createParamDecorator(
  (_data: unknown, ctx: ExecutionContext): number => {
    const adminContextService = ctx.switchToHttp().getRequest().adminContextService as AdminContextService;
    return adminContextService.getGroupId();
  }
);

export const GroupUid = createParamDecorator(
  (_data: unknown, ctx: ExecutionContext): string => {
    const adminContextService = ctx.switchToHttp().getRequest().adminContextService as AdminContextService;
    return adminContextService.getGroupUid();
  }
);
